import React, { useState } from 'react'
import {
    GoogleSignin,
    GoogleSigninButton,
    statusCodes,
  } from '@react-native-google-signin/google-signin'
  import { supabase } from '../lib/supabase'
  
  // Configure Google Sign-In once when module loads
  // GoogleSignin.configure({
  //   scopes: ['https://www.googleapis.com/auth/drive.readonly'],
  //   webClientId: "469644420373-v1pnl7l26a27umk3ccuk9ajf4t5p7o7l.apps.googleusercontent.com",
  //   // webClientId: "469644420373-aq1155q7a4stq875fokpqem534k1ukjj.apps.googleusercontent.com",
  // })
  GoogleSignin.configure({
    webClientId: '469644420373-v1pnl7l26a27umk3ccuk9ajf4t5p7o7l.apps.googleusercontent.com', // client ID of type WEB for your server. Required to get the `idToken` on the user object, and for offline access.
    scopes: ['https://www.googleapis.com/auth/drive.readonly'], // what API you want to access on behalf of the user, default is email and profile
    offlineAccess: true, // if you want to access Google API on behalf of the user FROM YOUR SERVER
    hostedDomain: '', // specifies a hosted domain restriction
    forceCodeForRefreshToken: false, // [Android] related to `serverAuthCode`, read the docs link below *.
    accountName: '', // [Android] specifies an account name on the device that should be used
    iosClientId: '<FROM DEVELOPER CONSOLE>', // [iOS] if you want to specify the client ID of type iOS (otherwise, it is taken from GoogleService-Info.plist)
    googleServicePlistPath: '', // [iOS] if you renamed your GoogleService-Info file, new name here, e.g. "GoogleService-Info-Staging"
    openIdRealm: '', // [iOS] The OpenID2 realm of the home web server. This allows Google to include the user's OpenID Identifier in the OpenID Connect ID token.
    profileImageSize: 120, // [iOS] The desired height (and width) of the profile image. Defaults to 120px
  });
  
  export default function GoogleSignInComponent(): React.JSX.Element {
    const [isSigningIn, setIsSigningIn] = useState(false)
  
    return (
      <GoogleSigninButton
        size={GoogleSigninButton.Size.Wide}
        color={GoogleSigninButton.Color.Dark}
        onPress={async () => {
          // Prevent multiple simultaneous sign-in attempts
          if (isSigningIn) {
            console.warn('Sign in already in progress - please wait')
            return
          }

          setIsSigningIn(true)
          try {
            await GoogleSignin.hasPlayServices()
            const userInfo = await GoogleSignin.signIn()
            
            if (userInfo.idToken) {
              const { error } = await supabase.auth.signInWithIdToken({
                provider: 'google',
                token: userInfo.idToken,
              })
              
              if (error) {
                console.error('Supabase auth error:', error.message)
              } else {
                console.log('✅ Successfully signed in')
              }
            } else {
              throw new Error('No ID token received from Google')
            }
          } catch (error: any) {
            if (error.code === statusCodes.SIGN_IN_CANCELLED) {
              console.log('Sign in cancelled by user')
            } else if (error.code === statusCodes.IN_PROGRESS) {
              console.warn('Sign in already in progress - please wait')
            } else if (error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
              console.error('Google Play services not available')
            } else {
              console.error('Sign in error:', error.message)
              console.error('Error code:', error.code)
              console.error('Full error object:', error)
            }
          } finally {
            setIsSigningIn(false)
          }
        }}
        disabled={isSigningIn}
      />
    )
  } 