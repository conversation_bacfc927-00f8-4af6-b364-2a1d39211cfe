import { useFonts } from 'expo-font';
import { Stack } from "expo-router";
import * as SplashScreen from 'expo-splash-screen';
import { useCallback } from 'react';
import { View } from 'react-native';
import '../global.css';

// Keep the splash screen visible while we fetch resources
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const [fontsLoaded, fontError] = useFonts({
    'HankenGrotesk-Regular': require('../assets/fonts/HankenGrotesk-Regular.ttf'),
    'HankenGrotesk-Bold': require('../assets/fonts/HankenGrotesk-Bold.ttf'),
  });

  const onLayoutRootView = useCallback(async () => {
    if (fontsLoaded || fontError) {
      await SplashScreen.hideAsync();
    }
  }, [fontsLoaded, fontError]);

  if (!fontsLoaded && !fontError) {
    return null;
  }

  return (
    <View onLayout={onLayoutRootView} style={{ flex: 1, backgroundColor: '#18181b' }}>
      <Stack
        screenOptions={{
          headerStyle: { backgroundColor: '#18181b' },
          headerTintColor: '#ffffff',
          contentStyle: { backgroundColor: '#18181b' }
        }}
      >
        <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
        <Stack.Screen 
          name="post/[id]" 
          options={{ 
            headerShown: true, 
            title: "Post"
          }} 
        />
        <Stack.Screen
          name="info"
          options={{
            headerShown: true,
            title: "Information"
          }}
        />
        <Stack.Screen
          name="signup"
          options={{
            headerShown: true,
            title: "Sign Up"
          }}
        />
        <Stack.Screen
          name="login"
          options={{
            headerShown: true,
            title: "Sign In"
          }}
        />
      </Stack>
    </View>
  );
}
